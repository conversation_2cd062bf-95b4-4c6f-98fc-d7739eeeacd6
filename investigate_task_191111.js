import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function investigateTask191111() {
  try {
    console.log('🔍 Investigating task 191111...\n');
    
    // Get the task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 191111)
      .single();
    
    if (taskError) {
      console.error('❌ Error fetching task:', taskError);
      return;
    }
    
    if (!task) {
      console.log('❌ Task 191111 not found');
      return;
    }
    
    console.log('📋 Task Details:');
    console.log('ID:', task.id);
    console.log('Task Type:', task.task_type);
    console.log('Status:', task.status);
    console.log('Created At:', task.created_at);
    console.log('Processed At:', task.processed_at);
    console.log('Scheduled At:', task.scheduled_at);
    console.log('Locked At:', task.locked_at);
    console.log('Locked By:', task.locked_by);
    console.log('Payload:', JSON.stringify(task.payload, null, 2));
    console.log('Result:', JSON.stringify(task.result, null, 2));
    
    // If it's a publish_disc task, get the disc details
    if (task.task_type === 'publish_disc' && task.payload && task.payload.id) {
      const discId = task.payload.id;
      console.log('\n🎯 Disc Details:');
      
      const { data: disc, error: discError } = await supabase
        .from('t_discs')
        .select('id, g_handle, g_title, shopify_uploaded_at, shopify_uploaded_notes, mps_id')
        .eq('id', discId)
        .single();
      
      if (discError) {
        console.error('❌ Error fetching disc:', discError);
      } else if (disc) {
        console.log('Disc ID:', disc.id);
        console.log('Handle:', disc.g_handle);
        console.log('Title:', disc.g_title);
        console.log('Shopify Uploaded At:', disc.shopify_uploaded_at);
        console.log('Shopify Upload Notes:', disc.shopify_uploaded_notes);
        console.log('MPS ID:', disc.mps_id);
        
        // Check if there are any Shopify products with this handle
        if (disc.g_handle) {
          console.log('\n🔍 Checking for existing Shopify products with handle:', disc.g_handle);
          
          // We can't directly query Shopify from here, but we can suggest what to check
          console.log('To check for duplicates, run this in your Shopify admin or via API:');
          console.log(`- Search for products with handle: "${disc.g_handle}"`);
          console.log(`- Look for products with handles like: "${disc.g_handle}-1", "${disc.g_handle}-2", etc.`);
        }
      }
    }
    
    // Check for any related error logs
    console.log('\n📝 Checking for related error logs...');
    const { data: errorLogs, error: logError } = await supabase
      .from('t_error_logs')
      .select('*')
      .ilike('context', `%${task.id}%`)
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (logError) {
      console.error('❌ Error fetching logs:', logError);
    } else if (errorLogs && errorLogs.length > 0) {
      console.log('Found related error logs:');
      errorLogs.forEach((log, index) => {
        console.log(`${index + 1}. [${log.created_at}] ${log.error_message}`);
        console.log(`   Context: ${log.context}`);
      });
    } else {
      console.log('No related error logs found');
    }
    
  } catch (error) {
    console.error('❌ Error investigating task:', error.message);
  }
}

investigateTask191111();
