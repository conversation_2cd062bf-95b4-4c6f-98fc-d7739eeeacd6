import dotenv from 'dotenv';

dotenv.config();

const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

/**
 * Execute a GraphQL request to Shopify
 */
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });

  const result = await response.json();
  if (result.errors) {
    throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

async function checkForDuplicates() {
  try {
    console.log('🔍 Checking for duplicate products for disc 423655...\n');
    
    const baseHandle = 'mvp-fission-trail-with-special-edition-trail-art-by-john-dorn-stamp-169.21g-white-d423655';
    
    // Check for the original handle
    console.log('Checking for original handle:', baseHandle);
    const originalQuery = `
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) {
          id
          handle
          title
          createdAt
          updatedAt
        }
      }
    `;
    
    const originalResult = await shopifyGraphQLRequest(originalQuery, { handle: baseHandle });
    
    if (originalResult.productByHandle) {
      console.log('✅ Found original product:');
      console.log('  ID:', originalResult.productByHandle.id);
      console.log('  Handle:', originalResult.productByHandle.handle);
      console.log('  Title:', originalResult.productByHandle.title);
      console.log('  Created:', originalResult.productByHandle.createdAt);
      console.log('  Updated:', originalResult.productByHandle.updatedAt);
    } else {
      console.log('❌ Original product not found');
    }
    
    // Check for potential duplicates with -1, -2, etc. suffixes
    console.log('\n🔍 Checking for potential duplicates...');
    
    const duplicateHandles = [
      `${baseHandle}-1`,
      `${baseHandle}-2`,
      `${baseHandle}-3`,
      `${baseHandle}-4`,
      `${baseHandle}-5`
    ];
    
    let duplicatesFound = 0;
    
    for (const handle of duplicateHandles) {
      console.log(`Checking: ${handle}`);
      
      const duplicateResult = await shopifyGraphQLRequest(originalQuery, { handle });
      
      if (duplicateResult.productByHandle) {
        duplicatesFound++;
        console.log(`  ⚠️ DUPLICATE FOUND:`, duplicateResult.productByHandle.handle);
        console.log(`    ID:`, duplicateResult.productByHandle.id);
        console.log(`    Created:`, duplicateResult.productByHandle.createdAt);
      } else {
        console.log(`  ✅ No product found with handle: ${handle}`);
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`  Original product: ${originalResult.productByHandle ? 'Found' : 'Not found'}`);
    console.log(`  Duplicates found: ${duplicatesFound}`);
    
    if (duplicatesFound > 0) {
      console.log(`\n⚠️ DUPLICATES DETECTED! This confirms the issue existed before the fix.`);
    } else {
      console.log(`\n✅ No duplicates found. The fix may have prevented the issue.`);
    }
    
  } catch (error) {
    console.error('❌ Error checking for duplicates:', error.message);
  }
}

checkForDuplicates();
