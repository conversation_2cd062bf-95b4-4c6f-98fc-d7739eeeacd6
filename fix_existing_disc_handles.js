import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('dry-run', {
    describe: 'Show what would be fixed without making changes',
    type: 'boolean',
    default: true
  })
  .option('limit', {
    describe: 'Maximum number of records to process',
    type: 'number',
    default: 100
  })
  .option('fix', {
    describe: 'Actually fix the handles (turns off dry-run)',
    type: 'boolean',
    default: false
  })
  .help()
  .alias('help', 'h')
  .argv;

const DRY_RUN = !argv.fix;
const LIMIT = argv.limit;

async function findProblematicHandles() {
  try {
    console.log('🔍 Searching for discs with problematic handles...\n');
    
    // Find discs with handles containing problematic characters
    const { data: discs, error } = await supabase
      .from('t_discs')
      .select('id, g_handle, g_title')
      .or(`g_handle.like.%|%,g_handle.like.%.%,g_handle.like.%'%,g_handle.like.%/%,g_handle.like.%&%,g_handle.like.%(%,g_handle.like.%)%,g_handle.like.%"%,g_handle.like.%#%,g_handle.like.%$%,g_handle.like.%+%,g_handle.like.%=%,g_handle.like.%?%,g_handle.like.%!%,g_handle.like.%*%,g_handle.like.%[%,g_handle.like.%]%,g_handle.like.%{%,g_handle.like.%}%,g_handle.like.%<%,g_handle.like.%>%,g_handle.like.%:%,g_handle.like.%;%,g_handle.like.%,%,g_handle.like.%^%,g_handle.like.%~%,g_handle.like.%\`%`)
      .not('g_handle', 'is', null)
      .limit(LIMIT)
      .order('id', { ascending: true });
    
    if (error) {
      console.error('❌ Error fetching discs:', error.message);
      return;
    }
    
    if (!discs || discs.length === 0) {
      console.log('✅ No discs found with problematic handles!');
      return;
    }
    
    console.log(`📋 Found ${discs.length} discs with problematic handles:\n`);
    
    const tasksToEnqueue = [];
    
    discs.forEach((disc, index) => {
      const originalHandle = disc.g_handle;
      const sanitizedHandle = sanitizeShopifyHandle(originalHandle);
      const hasProblems = originalHandle !== sanitizedHandle;
      
      if (hasProblems) {
        console.log(`${index + 1}. Disc ID: ${disc.id}`);
        console.log(`   Title: ${disc.g_title || 'N/A'}`);
        console.log(`   Current handle: "${originalHandle}"`);
        console.log(`   Fixed handle:   "${sanitizedHandle}"`);
        console.log('');
        
        tasksToEnqueue.push({
          task_type: 'generate_disc_title_pull_and_handle',
          payload: { id: disc.id },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString()
        });
      }
    });
    
    if (tasksToEnqueue.length === 0) {
      console.log('✅ All handles are already properly sanitized!');
      return;
    }
    
    console.log(`📊 Summary:`);
    console.log(`  - Total discs checked: ${discs.length}`);
    console.log(`  - Discs needing fixes: ${tasksToEnqueue.length}`);
    console.log(`  - Mode: ${DRY_RUN ? 'DRY RUN (no changes made)' : 'FIXING HANDLES'}`);
    
    if (DRY_RUN) {
      console.log('\n💡 To actually fix these handles, run:');
      console.log('   node fix_existing_disc_handles.js --fix');
      console.log('\n⚠️  This will enqueue generate_disc_title_pull_and_handle tasks for each problematic disc.');
    } else {
      console.log('\n🔧 Enqueueing tasks to fix handles...');
      
      // Enqueue tasks in batches
      const batchSize = 50;
      for (let i = 0; i < tasksToEnqueue.length; i += batchSize) {
        const batch = tasksToEnqueue.slice(i, i + batchSize);
        
        const { error: insertError } = await supabase
          .from('t_task_queue')
          .insert(batch);
        
        if (insertError) {
          console.error(`❌ Error enqueueing batch ${Math.floor(i / batchSize) + 1}:`, insertError.message);
        } else {
          console.log(`✅ Enqueued batch ${Math.floor(i / batchSize) + 1} (${batch.length} tasks)`);
        }
      }
      
      console.log(`\n✅ Successfully enqueued ${tasksToEnqueue.length} tasks to fix disc handles.`);
      console.log('   The task queue worker will process these and update the handles.');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

function sanitizeShopifyHandle(handle) {
  if (!handle) return handle;
  
  let sanitized = handle.toLowerCase();
  
  // Replace spaces with dashes
  sanitized = sanitized.replace(/ /g, '-');
  
  // Replace periods with dashes (important for weights like "169.21g")
  sanitized = sanitized.replace(/\./g, '-');
  
  // Remove other special characters that aren't allowed in Shopify handles
  sanitized = sanitized.replace(/'/g, '');
  sanitized = sanitized.replace(/\//g, '');
  sanitized = sanitized.replace(/\|/g, ''); // Remove pipes
  sanitized = sanitized.replace(/&/g, '-');
  sanitized = sanitized.replace(/\(/g, '');
  sanitized = sanitized.replace(/\)/g, '');
  sanitized = sanitized.replace(/"/g, '');
  sanitized = sanitized.replace(/%/g, '');
  sanitized = sanitized.replace(/#/g, '');
  sanitized = sanitized.replace(/\$/g, '');
  sanitized = sanitized.replace(/\+/g, '');
  sanitized = sanitized.replace(/=/g, '');
  sanitized = sanitized.replace(/\?/g, '');
  sanitized = sanitized.replace(/!/g, '');
  sanitized = sanitized.replace(/\*/g, '');
  sanitized = sanitized.replace(/\[/g, '');
  sanitized = sanitized.replace(/\]/g, '');
  sanitized = sanitized.replace(/\{/g, '');
  sanitized = sanitized.replace(/\}/g, '');
  sanitized = sanitized.replace(/</g, '');
  sanitized = sanitized.replace(/>/g, '');
  sanitized = sanitized.replace(/:/g, '');
  sanitized = sanitized.replace(/;/g, '');
  sanitized = sanitized.replace(/,/g, '');
  sanitized = sanitized.replace(/\^/g, '');
  sanitized = sanitized.replace(/~/g, '');
  sanitized = sanitized.replace(/`/g, '');
  
  // Replace multiple dashes with a single dash
  sanitized = sanitized.replace(/-+/g, '-');
  
  // Remove leading/trailing dashes
  sanitized = sanitized.replace(/^-+|-+$/g, '');
  
  return sanitized;
}

findProblematicHandles();
