# Supabase credentials
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Shopify GraphQL Admin API credentials
SHOPIFY_ENDPOINT=https://your-store.myshopify.com/admin/api/2024-01/graphql.json
SHOPIFY_ACCESS_TOKEN=your_access_token
SHOPIFY_LOCATION_ID=gid://shopify/Location/your_location_id

# Default location ID for Drop Zone Disc Golf LFK Retail Shop
# SHOPIFY_LOCATION_ID=gid://shopify/Location/63618220220
# This is the location at: 811 East 23rd Street, Suite E, Lawrence KS 66046, United States
# URL: https://admin.shopify.com/store/dzdiscs-new-releases/settings/locations/63618220220

# Amazon SP-API credentials
AMAZON_CLIENT_ID=your_amazon_client_id
AMAZON_CLIENT_SECRET=your_amazon_client_secret
AMAZON_REFRESH_TOKEN=your_amazon_refresh_token
AMAZON_SELLER_ID=your_amazon_seller_id
AMAZON_MARKETPLACE_ID=ATVPDKIKX0DER
AMAZON_REGION=us-east-1
AMAZON_SP_API_ENDPOINT=https://sellingpartnerapi-na.amazon.com

# Task Queue Worker Configuration
# DAEMON_MODE=true                # Set to 'false' to run worker once and exit (default: true)
# POLL_INTERVAL_MS=15000          # Polling interval in milliseconds for daemon mode (default: 5000)
