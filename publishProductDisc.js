// =================== Section 0: Parse Command-Line Arguments ===================
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// For Node 18+ the global fetch is available.
const args = minimist(process.argv.slice(2));
const discId = args.id;
if (!discId) {
  console.error('No disc id provided. Use --id=<discId>');
  process.exit(1);
}
console.log(`INFO: Received disc id: ${discId}`);

// =================== Section 1: Initialize Supabase Client ===================
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error("ERROR: Missing Supabase URL or key in environment variables.");
  process.exit(1);
}
console.log("INFO: Initializing Supabase client...");
const supabase = createClient(supabaseUrl, supabaseKey);

// =================== Section 2: Shopify Configuration ===================
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error("ERROR: Missing Shopify endpoint or access token.");
  process.exit(1);
}
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");
console.log(`INFO: Shopify products endpoint: ${productsEndpoint}`);

// =================== Section 3: Helper Function for Logging Errors ===================
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from('t_error_logs')
      .insert({ error_message: errorMessage, context });
    if (error) {
      console.error('Failed to log error to t_error_logs:', error);
    }
  } catch (err) {
    console.error('Exception while logging error:', err);
  }
}

// =================== Section 4: Helper Functions ===================

/**
 * Execute a GraphQL request to Shopify
 */
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });

  const result = await response.json();
  if (result.errors) {
    throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

/**
 * Check if a product exists on Shopify by handle using GraphQL.
 * Returns the product GID (e.g., "gid://shopify/Product/123456789") if found, otherwise null.
 */
async function getProductGIDByHandle(handle) {
  const query = `
    query getProductByHandle($handle: String!) {
      productByHandle(handle: $handle) {
        id
        handle
      }
    }
  `;
  const variables = { handle };
  const data = await shopifyGraphQLRequest(query, variables);

  console.log("DEBUG: GraphQL productByHandle result (data):", JSON.stringify(data, null, 2));

  if (data.productByHandle) {
    console.log("DEBUG: Found productByHandle with ID:", data.productByHandle.id, "and handle:", data.productByHandle.handle);
    return data.productByHandle.id; // GID format
  }
  console.log("DEBUG: productByHandle returned null (no matching product).");
  return null;
}

// =================== Section 5: Proceed with Publishing ===================
// Note: We're skipping the v_todo_discs check since the task queue worker
// has already verified that the disc is ready to be published
console.log(`INFO: Proceeding with publishing disc id ${discId} to Shopify...`);

// =================== Section 6: Retrieve t_discs Record ===================
console.log(`INFO: Fetching t_discs record for id=${discId}`);
const { data: discRecord, error: discError } = await supabase
  .from('t_discs')
  .select('*')
  .eq('id', discId)
  .single();
if (discError) {
  console.error(`ERROR: Fetching t_discs record: ${discError.message}`);
  process.exit(1);
}
console.log(`INFO: t_discs record: ${JSON.stringify(discRecord)}`);

// =================== Section 6: Retrieve t_order_sheet_lines Record ===================
let orderSheetRecord = null;
if (discRecord.order_sheet_line_id) {
  console.log(`INFO: Fetching t_order_sheet_lines record for id=${discRecord.order_sheet_line_id}`);
  const { data, error } = await supabase
    .from('t_order_sheet_lines')
    .select('*')
    .eq('id', discRecord.order_sheet_line_id)
    .single();
  if (error) {
    console.warn(`WARNING: Could not fetch t_order_sheet_lines record: ${error.message}`);
  } else {
    orderSheetRecord = data;
    console.log(`INFO: t_order_sheet_lines record: ${JSON.stringify(orderSheetRecord)}`);
  }
} else {
  console.log("INFO: No order_sheet_line_id present on t_discs record.");
}

// =================== Section 7: Retrieve t_mps Record ===================
console.log(`INFO: Fetching t_mps record for id=${discRecord.mps_id}`);
const { data: mpsRecord, error: mpsError } = await supabase
  .from('t_mps')
  .select('*')
  .eq('id', discRecord.mps_id)
  .single();
if (mpsError) {
  console.error(`ERROR: Fetching t_mps record: ${mpsError.message}`);
  process.exit(1);
}
console.log(`INFO: t_mps record loaded for MPS ID: ${mpsRecord.id}`);


// =================== Section 8: Retrieve Handle from t_mps ===================
console.log(`INFO: Using g_handle from t_mps record for MPS id ${mpsRecord.id}...`);
const vMPSHandle = mpsRecord.g_handle;
console.log(`INFO: Retrieved g_handle from t_mps: ${vMPSHandle}`);

// =================== Section 8.0: Retrieve blurb_with_link from t_mps ===================
console.log(`INFO: Using g_blurb_with_link from t_mps record for MPS id ${mpsRecord.id}...`);
const vMPSBlurbWithLink = mpsRecord.g_blurb_with_link || "";
console.log(`INFO: Retrieved g_blurb_with_link from t_mps: ${vMPSBlurbWithLink}`);


// =================== Section 8.1: Retrieve Handle from t_discs ===================
console.log(`INFO: Using g_handle from t_discs record for disc id ${discRecord.id}...`);
const discHandle = discRecord.g_handle;
console.log(`INFO: Retrieved g_handle from t_discs: ${discHandle}`);


// =================== Section 8.2: Retrieve Title from t_discs ===================
console.log(`INFO: Using g_title from t_discs record for disc id ${discRecord.id}...`);
const discTitle = discRecord.g_title;
console.log(`INFO: Retrieved g_title from t_discs: ${discTitle}`);


// =================== Section 8.3: Retrieve SEO Metafield Description from v_discs ===================
console.log(`INFO: Fetching SEO metafield description from v_discs for disc id ${discRecord.id}...`);
const { data: vDiscsSeoData, error: vDiscsSeoError } = await supabase
  .from('v_discs')
  .select('seo_metafield_description')
  .eq('id', discRecord.id)
  .single();
if (vDiscsSeoError) {
  console.error(`ERROR: Fetching SEO metafield description from v_discs: ${vDiscsSeoError.message}`);
  process.exit(1);
}
const seoMetafieldDescription = vDiscsSeoData.seo_metafield_description;
console.log(`INFO: Retrieved SEO metafield description: ${seoMetafieldDescription}`);



// =================== Section 9: Retrieve t_plastics Record ===================
// First, fetch all columns from the t_plastics table.
console.log(`INFO: Fetching t_plastics record for id=${mpsRecord.plastic_id} from t_plastics...`);
const { data: plasticTableData, error: plasticTableError } = await supabase
  .from('t_plastics')
  .select('*')
  .eq('id', mpsRecord.plastic_id)
  .single();
if (plasticTableError) {
  console.error(`ERROR: Fetching t_plastics record: ${plasticTableError.message}`);
  process.exit(1);
}
console.log(`INFO: t_plastics record loaded for plastic ID: ${plasticTableData.id}`);

// Then, fetch the blurb_with_link field from the v_plastics view.
console.log(`INFO: Fetching blurb_with_link from v_plastics for id=${mpsRecord.plastic_id}...`);
const { data: plasticViewData, error: plasticViewError } = await supabase
  .from('v_plastics')
  .select('blurb_with_link')
  .eq('id', mpsRecord.plastic_id)
  .single();
if (plasticViewError) {
  console.warn(`WARNING: Could not fetch v_plastics record: ${plasticViewError.message}`);
}

// Merge the blurb_with_link field from the view into the main plastic record.
const plasticData = { ...plasticTableData, blurb_with_link: plasticViewData ? plasticViewData.blurb_with_link : "" };
console.log(`INFO: Combined plastic record for: ${plasticData.plastic}`);




// =================== Section 10: Retrieve t_molds Record ===================
// First, fetch all columns from the t_molds table.
console.log(`INFO: Fetching t_molds record for id=${mpsRecord.mold_id} from t_molds...`);
const { data: moldTableData, error: moldTableError } = await supabase
  .from('t_molds')
  .select('*')
  .eq('id', mpsRecord.mold_id)
  .single();
if (moldTableError) {
  console.error(`ERROR: Fetching t_molds record: ${moldTableError.message}`);
  process.exit(1);
}
console.log(`INFO: t_molds record loaded for mold: ${moldTableData.mold}`);

// Then, fetch the blurb_with_link field from the v_molds view.
console.log(`INFO: Fetching blurb_with_link from v_molds for id=${mpsRecord.mold_id}...`);
const { data: moldViewData, error: moldViewError } = await supabase
  .from('v_molds')
  .select('blurb_with_link')
  .eq('id', mpsRecord.mold_id)
  .single();
if (moldViewError) {
  console.warn(`WARNING: Could not fetch v_molds record: ${moldViewError.message}`);
}

// Merge the blurb_with_link field from the view into the main mold record.
const moldData = { ...moldTableData, blurb_with_link: moldViewData ? moldViewData.blurb_with_link : "" };
console.log(`INFO: Combined mold record for: ${moldData.mold}`);



// =================== Section 11: Retrieve t_stamps Record ===================
console.log(`INFO: Fetching t_stamps record for id=${mpsRecord.stamp_id}...`);
const { data: stampData, error: stampError } = await supabase
  .from('t_stamps')
  .select('*, blurb')
  .eq('id', mpsRecord.stamp_id)
  .single();
if (stampError) {
  console.error(`ERROR: Fetching t_stamps record: ${stampError.message}`);
  process.exit(1);
}
console.log(`INFO: t_stamps record: ${JSON.stringify(stampData)}`);

// =================== Section 11.5: Retrieve t_players Record from Stamp ===================
let stampPlayerData = null;
if (stampData.player_id) {
  console.log(`INFO: Fetching t_players record for stamp player_id=${stampData.player_id}`);
  const { data, error } = await supabase
    .from('t_players')
    .select('*')
    .eq('id', stampData.player_id)
    .single();
  if (error) {
    console.warn(`WARNING: Could not fetch t_players record for stamp: ${error.message}`);
  } else {
    stampPlayerData = data;
    console.log(`INFO: t_players record from stamp: ${JSON.stringify(stampPlayerData)}`);
  }
} else {
  console.log("INFO: No player_id available from t_stamps.");
}

// =================== Section 12: Retrieve t_brands Record ===================
console.log(`INFO: Fetching t_brands record for id=${moldData.brand_id}...`);
const { data: brandData, error: brandError } = await supabase
  .from('t_brands')
  .select('*')
  .eq('id', moldData.brand_id)
  .single();
if (brandError) {
  console.error(`ERROR: Fetching t_brands record: ${brandError.message}`);
  process.exit(1);
}
console.log(`INFO: t_brands record: ${JSON.stringify(brandData)}`);

// =================== Section 13: Retrieve t_colors Record ===================
let colorData = { color: "unknown" };
if (discRecord.color_id) {
  console.log(`INFO: Fetching t_colors record for id=${discRecord.color_id}`);
  const { data, error } = await supabase
    .from('t_colors')
    .select('*')
    .eq('id', discRecord.color_id)
    .single();
  if (error) {
    console.warn(`WARNING: Could not fetch t_colors record: ${error.message}`);
  } else {
    colorData = data;
    console.log(`INFO: t_colors record: ${JSON.stringify(colorData)}`);
  }
} else {
  console.log("INFO: No color_id available from t_discs; using default 'unknown'.");
}

// =================== Section 14: Retrieve t_players Record (DEPRECATED) ===================
// NOTE: t_order_sheet_lines does not have a player_id field. Player data should come from stamps.
// This section is kept for backward compatibility but will not find any player data.
let playerData = null;
console.log("INFO: Skipping player lookup from t_order_sheet_lines (field does not exist). Player data comes from stamps instead.");

// =================== Section 15: Retrieve t_colorfeatures Record ===================
let colorFeature = "";
if (discRecord.color_feature_id) {
  console.log(`INFO: Fetching t_colorfeatures record for id=${discRecord.color_feature_id}`);
  const { data, error } = await supabase
    .from('t_colorfeatures')
    .select('*')
    .eq('id', discRecord.color_feature_id)
    .single();
  if (error) {
    console.warn(`WARNING: Could not fetch t_colorfeatures record: ${error.message}`);
  } else {
    colorFeature = data.feature || "";
    console.log(`INFO: t_colorfeatures record: ${JSON.stringify(data)}`);
  }
} else {
  console.log("INFO: No color_feature_id on t_discs record.");
}

// =================== Section 16: Retrieve t_images Record ===================
console.log(`INFO: Fetching t_images record for table 't_discs', record_id=${discRecord.id}`);
const { data: imageRecordData, error: imageRecordError } = await supabase
  .from('t_images')
  .select('*')
  .eq('table_name', 't_discs')
  .eq('record_id', discRecord.id)
  .maybeSingle();
if (imageRecordError) {
  console.warn(`WARNING: Error fetching t_images record: ${imageRecordError.message}`);
} else {
  console.log(`INFO: t_images record: ${JSON.stringify(imageRecordData)}`);
}

// =================== Section 17: Compute Product Image URL ===================
let productImage = "";
if (discRecord.description === "Local Love") {
  productImage = "https://d3f34rkxix3zin.cloudfront.net/shopify/discs/local_love.jpg";
} else if (discRecord.ImageURLonMagento) {
  productImage = discRecord.ImageURLonMagento;
} else if (discRecord.image_file_name) {
  const folder = discRecord.image_file_name.substring(0, 6);
  productImage = `https://d3f34rkxix3zin.cloudfront.net/shopify/discs/${folder}/${discRecord.image_file_name}.jpg`;
} else {
  productImage = `https://d3f34rkxix3zin.cloudfront.net/shopify/discs/${discRecord.id}.jpg`;
}
console.log(`INFO: Product image URL: ${productImage}`);

// =================== Section 18: Compute Handle & Title (deprecated) ===================




// =================== Section 19: Compute Tags ===================
const discountFlag = mpsRecord["30_percent_discount_allowed"] ? "Y" : "N";
let tags = `disc_type_${moldData.type},disc_weight_${discRecord.weight},disc_plastic_${plasticData.plastic},disc_color_${colorData.color},disc_mold_${moldData.mold},disc_brand_${brandData.brand},30ok_${discountFlag}`;

// NOTE: Player tags from order sheet line removed - t_order_sheet_lines does not have player_id field
// Player data now comes from stamps only

// Add player tags from stamp (new logic)
if (stampPlayerData) {
  tags += `,player_${stampPlayerData.name}`;
  console.log(`INFO: Added player tag from stamp: player_${stampPlayerData.name}`);
} else {
  console.log("INFO: No player data from stamp; skipping stamp player tags.");
}

if (stampData.tags) {
  tags += `,${stampData.tags}`;
} else {
  console.log("INFO: No stamp tags; skipping.");
}
tags += `,disc_stamp_${stampData.stamp},Color_Family_${colorData.color}`;
tags += (discRecord.description === "Local Love") ? ",class_locallove" : ",class_discwithimage";
if (mpsRecord.limited_release_auto_bag_and_card === true) {
  tags += ",autobagandcard";
}
tags += `,DiscFN_Speed_${moldData.speed},DiscFN_Glide_${moldData.glide},DiscFN_Turn_${moldData.turn},DiscFN_Fade_${moldData.fade}`;
console.log(`INFO: Generated tags: ${tags}`);

// =================== Section 20: Compute Template Suffix ===================
const templateSuffix = moldData.video_url ? "disc-with-image-with-vid" : "disc-with-image";

// =================== Section 21: Compute Option Fields ===================
const option1Name = "Color";
const option1Value = `${discRecord.color_modifier || ""} ${colorData.color || ""}`.trim();
const option2Name = "Weight";
const option2Value = discRecord.weight != null ? discRecord.weight.toString() + "g" : "unknown";
console.log(`INFO: Option 1 (Color): ${option1Value}`);
console.log(`INFO: Option 2 (Weight): ${option2Value}`);

// =================== Section 22: Compute Variant Details ===================
const variantSKU = "D" + discRecord.id;
let variantBarcode = "";
if (orderSheetRecord && orderSheetRecord.upc) {
  variantBarcode = orderSheetRecord.upc;
} else if (mpsRecord.upc) {
  variantBarcode = mpsRecord.upc;
} else {
  console.warn("INFO: No UPC found in order sheet or mps; using placeholder 'unknown-upc'.");
  variantBarcode = "unknown-upc";
}
variantBarcode = "'" + variantBarcode;
// Convert disc weight from grams to pounds with 2 decimal places
// Add 5 grams for packaging, then convert to pounds (1 gram = 0.00220462 pounds)
const variantWeight = Math.round((discRecord.weight + 5) * 0.00220462 * 100) / 100;
const variantPrice = mpsRecord.val_override_retail_price != null
  ? mpsRecord.val_override_retail_price
  : plasticData.val_retail_price || 0;
const variantCompareAtPrice = "";
const variantTaxable = true;
const variantInventoryTracker = "shopify";
const variantInventoryPolicy = "deny";
const variantFulfillmentService = "manual";
const variantRequiresShipping = true;
const variantInventoryQty = 1;

// =================== Section 23: Compute Image Alt Text ===================
const imageAltText = `${brandData.brand} ${plasticData.plastic} ${moldData.mold}` +
  ((stampData.stamp && stampData.stamp !== "Stock") ? ` with ${stampData.stamp}${(colorData.color === "Printed" ? " Print" : " Stamp")}` : "") +
  ` - ${discRecord.weight} ${colorData.color !== "Printed" ? colorData.color : ""} ${colorFeature}`.trim();


// =================== 23.1: Compute body_html ===================
// =================== New: Compute body_html ===================
// Use t_discs.description only if it is not "." or empty (or null).
let body_html = "";
if (discRecord.description && discRecord.description.trim() !== "" && discRecord.description.trim() !== ".") {
  body_html = `<p>This Disc: ${discRecord.description}</p>`;
}
// Append the additional blurbs from the views and the stamps table.
body_html += vMPSBlurbWithLink || "";
body_html += moldData.blurb_with_link || "";
body_html += plasticData.blurb_with_link || "";
body_html += stampData.blurb || "";

console.log("DEBUG: Computed body_html:", body_html);

// =================== Sanitize Metafields ===================
const sanitizedSeoMetafieldDescription = (seoMetafieldDescription || "").replace(/[\r\n]+/g, ' ');



// =================== Section 24: Assemble Final Product Data ===================
const productData = {
  title: discTitle, // Using the disc title retrieved from v_discs
  handle: discHandle, // Using the disc handle retrieved from v_discs
  body_html: body_html, // Use the computed HTML content here
  vendor: brandData.brand,
  product_type: "Disc",
  standardized_product_type: "Disc Golf", // the new "Category" field
  tags: tags,
  published: true,
  published_scope: "global",
  template_suffix: templateSuffix,
  category_id: "5120", // Placeholder if needed
  options: [
    { name: option1Name, values: [ option1Value ] },
    { name: option2Name, values: [ option2Value ] }
  ],
  variants: [
    {
      option1: option1Value,
      option2: option2Value,
      sku: variantSKU,
      barcode: variantBarcode,
      weight: variantWeight,
      weight_unit: "lb",
      price: variantPrice,
      compare_at_price: variantCompareAtPrice,
      taxable: variantTaxable,
      inventory_management: variantInventoryTracker,
      inventory_policy: variantInventoryPolicy,
      fulfillment_service: variantFulfillmentService,
      requires_shipping: variantRequiresShipping,
      inventory_quantity: variantInventoryQty
    }
  ],
  images: [
    { src: productImage, alt: imageAltText }
  ],
metafields: [
  { key: "videourl", value: moldData.video_url || "no-video-url", type: "single_line_text_field", namespace: "my_fields" },
  { key: "seo_canonical", value: "https://www.dzdiscs.com/collections/" + vMPSHandle, type: "single_line_text_field", namespace: "my_fields" },
  { key: "description_tag", value: sanitizedSeoMetafieldDescription, type: "single_line_text_field", namespace: "global" }
]

};

console.log("INFO: Publishing immediately (no scheduling logic).");
console.log("INFO: Final product data - Title:", productData.title, "Handle:", productData.handle);

// =================== Section 25: Check if Product Already Exists ===================
console.log("INFO: Checking if product already exists on Shopify with handle:", productData.handle);
try {
  const existingProductGID = await getProductGIDByHandle(productData.handle);

  if (existingProductGID) {
    console.log("INFO: Product already exists on Shopify - treating as successful upload.");
    console.log("INFO: Existing product GID:", existingProductGID);
    console.log("INFO: Handle:", productData.handle);

    // Update t_discs record to mark as successfully uploaded (already exists)
    const successNote = "Success! Product already exists on Shopify with this handle.";
    console.log(`INFO: Updating t_discs record id=${discRecord.id} with success note for existing product...`);
    const { error: updateError } = await supabase
      .from("t_discs")
      .update({
        shopify_uploaded_at: new Date().toISOString(),
        shopify_uploaded_notes: successNote
      })
      .eq("id", discRecord.id);
    if (updateError) {
      console.error(`ERROR: Failed to update t_discs record for id=${discRecord.id}: ${updateError.message}`);
    } else {
      console.log(`INFO: Successfully updated t_discs record for id=${discRecord.id} - marked as already uploaded`);
    }

    // Exit successfully since the product already exists
    console.log("INFO: publishProductDisc process completed successfully (product already exists).");
    console.log(JSON.stringify({ status: 'complete', message: 'Product already exists on Shopify', discId }));
    process.exit(0);
  }

  console.log("INFO: No existing product found with handle:", productData.handle);
  console.log("INFO: Proceeding to create new product...");

} catch (checkError) {
  console.error("ERROR: Failed to check for existing product:", checkError.message);
  // Continue with creation attempt - if check fails, we'll try to create and handle any errors
  console.log("INFO: Continuing with product creation despite check failure...");
}

// =================== Section 26: Create Product on Shopify ===================
try {
  const response = await fetch(productsEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify({ command: "MERGE", product: productData })
  });
  const result = await response.json();
  if (!response.ok) {
    throw new Error(`Error creating product: ${JSON.stringify(result)}`);
  }
  console.log("INFO: Successfully created product with ID:", result.product?.id || 'unknown');

  // =================== Section 27: Update t_discs Record with Shopify Upload Info ===================
  const successNote = "Success! Shopify product processed via publishProductDisc.js.";
  console.log(`INFO: Updating t_discs record id=${discRecord.id} with shopify upload info...`);
  const { error: updateError } = await supabase
    .from("t_discs")
    .update({
      shopify_uploaded_at: new Date().toISOString(),
      shopify_uploaded_notes: successNote
    })
    .eq("id", discRecord.id);
  if (updateError) {
    console.error(`ERROR: Failed to update t_discs record for id=${discRecord.id}: ${updateError.message}`);
  } else {
    console.log(`INFO: Successfully updated t_discs record for id=${discRecord.id}`);
  }
} catch (err) {
  console.error("ERROR: Failed to create product:", err.message);
  // =================== Section 28: On Failure, Update t_discs Record with Failure Note ===================
  const failureNote = `Error creating product: ${err.message}`;
  const { error: updateError } = await supabase
    .from("t_discs")
    .update({
      shopify_uploaded_notes: failureNote,
      shopify_uploaded_at: null
    })
    .eq("id", discRecord.id);
  if (updateError) {
    console.error(`ERROR: Failed to update t_discs record for id=${discRecord.id} with failure note: ${updateError.message}`);
  } else {
    console.log(`INFO: Updated t_discs record for id=${discRecord.id} with failure note.`);
  }
  process.exit(1);
}

// =================== Section 29: Global Error Handler ===================
process.on('unhandledRejection', async (err) => {
  console.error('ERROR: Unexpected error:', err);
  await logError(err.message, 'Unexpected error in main function of publishProductDisc.js');
  process.exit(1);
});
console.log("INFO: publishProductDisc process completed successfully.");

// =================== Section 30: Exit with Success Code ===================
console.log(JSON.stringify({ status: 'complete', message: 'Script executed successfully', discId }));
process.exit(0);

