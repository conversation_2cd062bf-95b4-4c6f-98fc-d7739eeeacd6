import dotenv from 'dotenv';

dotenv.config();

const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

/**
 * Execute a GraphQL request to Shopify
 */
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });

  const result = await response.json();
  if (result.errors) {
    throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

async function searchForProducts() {
  try {
    console.log('🔍 Searching for products related to disc 423655...\n');
    
    // Search for products containing the disc ID
    console.log('1. Searching for products containing "d423655"...');
    
    const searchQuery = `
      query searchProducts($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              handle
              title
              createdAt
              updatedAt
              variants(first: 5) {
                edges {
                  node {
                    id
                    sku
                  }
                }
              }
            }
          }
        }
      }
    `;
    
    // Search by handle containing the disc ID
    let searchResult = await shopifyGraphQLRequest(searchQuery, { 
      query: 'handle:*d423655*', 
      first: 10 
    });
    
    if (searchResult.products.edges.length > 0) {
      console.log('✅ Found products with handle containing "d423655":');
      searchResult.products.edges.forEach((edge, index) => {
        const product = edge.node;
        console.log(`  ${index + 1}. Handle: ${product.handle}`);
        console.log(`     Title: ${product.title}`);
        console.log(`     ID: ${product.id}`);
        console.log(`     Created: ${product.createdAt}`);
        if (product.variants.edges.length > 0) {
          console.log(`     SKU: ${product.variants.edges[0].node.sku}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ No products found with handle containing "d423655"');
    }
    
    // Search by SKU
    console.log('\n2. Searching for products with SKU "D423655"...');
    
    searchResult = await shopifyGraphQLRequest(searchQuery, { 
      query: 'sku:D423655', 
      first: 10 
    });
    
    if (searchResult.products.edges.length > 0) {
      console.log('✅ Found products with SKU "D423655":');
      searchResult.products.edges.forEach((edge, index) => {
        const product = edge.node;
        console.log(`  ${index + 1}. Handle: ${product.handle}`);
        console.log(`     Title: ${product.title}`);
        console.log(`     ID: ${product.id}`);
        console.log(`     Created: ${product.createdAt}`);
        if (product.variants.edges.length > 0) {
          console.log(`     SKU: ${product.variants.edges[0].node.sku}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ No products found with SKU "D423655"');
    }
    
    // Search by title containing "Trail"
    console.log('\n3. Searching for products with title containing "Trail" and "John Dorn"...');
    
    searchResult = await shopifyGraphQLRequest(searchQuery, { 
      query: 'title:*Trail* AND title:*John Dorn*', 
      first: 10 
    });
    
    if (searchResult.products.edges.length > 0) {
      console.log('✅ Found products with title containing "Trail" and "John Dorn":');
      searchResult.products.edges.forEach((edge, index) => {
        const product = edge.node;
        console.log(`  ${index + 1}. Handle: ${product.handle}`);
        console.log(`     Title: ${product.title}`);
        console.log(`     ID: ${product.id}`);
        console.log(`     Created: ${product.createdAt}`);
        if (product.variants.edges.length > 0) {
          console.log(`     SKU: ${product.variants.edges[0].node.sku}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ No products found with title containing "Trail" and "John Dorn"');
    }
    
    // Check recent products (last 24 hours)
    console.log('\n4. Checking recent products (last 24 hours)...');
    
    const recentQuery = `
      query recentProducts($first: Int!) {
        products(first: $first, sortKey: CREATED_AT, reverse: true) {
          edges {
            node {
              id
              handle
              title
              createdAt
              variants(first: 1) {
                edges {
                  node {
                    sku
                  }
                }
              }
            }
          }
        }
      }
    `;
    
    const recentResult = await shopifyGraphQLRequest(recentQuery, { first: 20 });
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const recentProducts = recentResult.products.edges.filter(edge => {
      const createdAt = new Date(edge.node.createdAt);
      return createdAt > yesterday;
    });
    
    if (recentProducts.length > 0) {
      console.log(`✅ Found ${recentProducts.length} products created in the last 24 hours:`);
      recentProducts.forEach((edge, index) => {
        const product = edge.node;
        console.log(`  ${index + 1}. Handle: ${product.handle}`);
        console.log(`     Title: ${product.title.substring(0, 80)}...`);
        console.log(`     Created: ${product.createdAt}`);
        if (product.variants.edges.length > 0) {
          console.log(`     SKU: ${product.variants.edges[0].node.sku}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ No products created in the last 24 hours');
    }
    
  } catch (error) {
    console.error('❌ Error searching for products:', error.message);
  }
}

searchForProducts();
